import { betterAuth } from "better-auth";
import { admin, organization } from "better-auth/plugins";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import db from "@/db";
import env from "@/env";

// Minimal Better Auth server instance. It will expose handlers we can mount under /api/auth.
export const auth = betterAuth({
  database: drizzleAdapter(db, {
    provider: "pg", // or "mysql", "sqlite"
}),
  baseURL: process.env.BETTER_AUTH_URL || `http://localhost:${env.PORT}`,
  secret: process.env.BETTER_AUTH_SECRET || "dev-secret-change-me",
  plugins: [
    admin(),
    organization(),
  ],
});

export type BetterAuth = typeof auth;


