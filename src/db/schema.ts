import { pgTable, index, foreignKey, uuid, text, boolean, timestamp, integer, jsonb, unique, numeric, date, pgSchema } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"

export const user = pgTable("user", {
	id: text("id").primaryKey(),
	name: text("name").notNull(),
	email: text("email").notNull().unique(),
	emailVerified: boolean("email_verified")
	  .$defaultFn(() => false)
	  .notNull(),
	image: text("image"),
	createdAt: timestamp("created_at")
	  .$defaultFn(() => /* @__PURE__ */ new Date())
	  .notNull(),
	updatedAt: timestamp("updated_at")
	  .$defaultFn(() => /* @__PURE__ */ new Date())
	  .notNull(),
	role: text("role"),
	banned: boolean("banned"),
	banReason: text("ban_reason"),
	banExpires: timestamp("ban_expires"),
  });
  
  export const session = pgTable("session", {
	id: text("id").primaryKey(),
	expiresAt: timestamp("expires_at").notNull(),
	token: text("token").notNull().unique(),
	createdAt: timestamp("created_at").notNull(),
	updatedAt: timestamp("updated_at").notNull(),
	ipAddress: text("ip_address"),
	userAgent: text("user_agent"),
	userId: text("user_id")
	  .notNull()
	  .references(() => user.id, { onDelete: "cascade" }),
	impersonatedBy: text("impersonated_by"),
	activeOrganizationId: text("active_organization_id"),
  });
  
  export const account = pgTable("account", {
	id: text("id").primaryKey(),
	accountId: text("account_id").notNull(),
	providerId: text("provider_id").notNull(),
	userId: text("user_id")
	  .notNull()
	  .references(() => user.id, { onDelete: "cascade" }),
	accessToken: text("access_token"),
	refreshToken: text("refresh_token"),
	idToken: text("id_token"),
	accessTokenExpiresAt: timestamp("access_token_expires_at"),
	refreshTokenExpiresAt: timestamp("refresh_token_expires_at"),
	scope: text("scope"),
	password: text("password"),
	createdAt: timestamp("created_at").notNull(),
	updatedAt: timestamp("updated_at").notNull(),
  });
  
  export const verification = pgTable("verification", {
	id: text("id").primaryKey(),
	identifier: text("identifier").notNull(),
	value: text("value").notNull(),
	expiresAt: timestamp("expires_at").notNull(),
	createdAt: timestamp("created_at").$defaultFn(
	  () => /* @__PURE__ */ new Date(),
	),
	updatedAt: timestamp("updated_at").$defaultFn(
	  () => /* @__PURE__ */ new Date(),
	),
  });


export const listingNotes = pgTable("listing_notes", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	listingId: uuid("listing_id").notNull(),
	workspaceId: uuid("workspace_id").notNull(),
	createdBy: uuid("created_by").notNull(),
	content: text("content").notNull(),
	mentions: text("mentions").array().default([""]),
	isPrivate: boolean("is_private").default(false),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
},
(table) => {
	return {
		idxListingNotesCreatedAt: index("idx_listing_notes_created_at").using("btree", table.createdAt.asc().nullsLast()),
		idxListingNotesCreatedBy: index("idx_listing_notes_created_by").using("btree", table.createdBy.asc().nullsLast()),
		idxListingNotesListingId: index("idx_listing_notes_listing_id").using("btree", table.listingId.asc().nullsLast()),
		idxListingNotesWorkspaceId: index("idx_listing_notes_workspace_id").using("btree", table.workspaceId.asc().nullsLast()),
		listingNotesListingIdFkey: foreignKey({
			columns: [table.listingId],
			foreignColumns: [listings.id],
			name: "listing_notes_listing_id_fkey"
		}).onDelete("cascade"),
		listingNotesWorkspaceIdFkey: foreignKey({
			columns: [table.workspaceId],
			foreignColumns: [workspaces.id],
			name: "listing_notes_workspace_id_fkey"
		}).onDelete("cascade"),
		listingNotesCreatedByFkey: foreignKey({
			columns: [table.createdBy],
			foreignColumns: [users.id],
			name: "listing_notes_created_by_fkey"
		}),
	}
});

export const files = pgTable("files", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	workspaceId: uuid("workspace_id").notNull(),
	uploadedBy: uuid("uploaded_by").notNull(),
	fileName: text("file_name").notNull(),
	originalName: text("original_name").notNull(),
	mimeType: text("mime_type").notNull(),
	fileSize: integer("file_size").notNull(),
	storagePath: text("storage_path").notNull(),
	storageUrl: text("storage_url"),
	fileType: text("file_type").notNull(),
	entityType: text("entity_type"),
	entityId: uuid("entity_id"),
	isPublic: boolean("is_public").default(false),
	metadata: jsonb("metadata").default({}),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
},
(table) => {
	return {
		idxFilesCreatedAt: index("idx_files_created_at").using("btree", table.createdAt.asc().nullsLast()),
		idxFilesEntity: index("idx_files_entity").using("btree", table.entityType.asc().nullsLast(), table.entityId.asc().nullsLast()),
		idxFilesFileType: index("idx_files_file_type").using("btree", table.fileType.asc().nullsLast()),
		idxFilesUploadedBy: index("idx_files_uploaded_by").using("btree", table.uploadedBy.asc().nullsLast()),
		idxFilesWorkspaceId: index("idx_files_workspace_id").using("btree", table.workspaceId.asc().nullsLast()),
		filesWorkspaceIdFkey: foreignKey({
			columns: [table.workspaceId],
			foreignColumns: [workspaces.id],
			name: "files_workspace_id_fkey"
		}).onDelete("cascade"),
		filesUploadedByFkey: foreignKey({
			columns: [table.uploadedBy],
			foreignColumns: [users.id],
			name: "files_uploaded_by_fkey"
		}),
	}
});


export const organization = pgTable("organization", {
	id: text("id").primaryKey(),
	name: text("name").notNull(),
	slug: text("slug").unique(),
	logo: text("logo"),
	createdAt: timestamp("created_at").notNull(),
	metadata: text("metadata"),
  });
  
  export const member = pgTable("member", {
	id: text("id").primaryKey(),
	organizationId: text("organization_id")
	  .notNull()
	  .references(() => organization.id, { onDelete: "cascade" }),
	userId: text("user_id")
	  .notNull()
	  .references(() => user.id, { onDelete: "cascade" }),
	role: text("role").default("member").notNull(),
	createdAt: timestamp("created_at").notNull(),
  });
  
  export const invitation = pgTable("invitation", {
	id: text("id").primaryKey(),
	organizationId: text("organization_id")
	  .notNull()
	  .references(() => organization.id, { onDelete: "cascade" }),
	email: text("email").notNull(),
	role: text("role"),
	status: text("status").default("pending").notNull(),
	expiresAt: timestamp("expires_at").notNull(),
	inviterId: text("inviter_id")
	  .notNull()
	  .references(() => user.id, { onDelete: "cascade" }),
  });

export const listings = pgTable("listings", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	workspaceId: uuid("workspace_id").notNull(),
	createdBy: uuid("created_by").notNull(),
	assignedTo: uuid("assigned_to"),
	// Core business listing fields
	businessName: text("business_name").notNull(),
	industry: text("industry").notNull(),
	askingPrice: numeric("asking_price", { precision: 12, scale: 2 }),
	cashFlowSde: numeric("cash_flow_sde", { precision: 12, scale: 2 }),
	annualRevenue: numeric("annual_revenue", { precision: 12, scale: 2 }),
	status: text("status").default('draft'), // Active, Under Contract, Sold, Confidential, etc.
	generalLocation: text("general_location"), // City/State/Region for confidentiality
	yearEstablished: integer("year_established"),
	employees: integer("employees"),
	ownerHoursWeek: integer("owner_hours_week"),
	dateListed: date("date_listed"),
	daysListed: integer("days_listed"), // Auto-calculated field
	// Legacy fields (keeping for compatibility)
	title: text("title"), // Can be used as alternate to businessName
	description: text("description"),
	price: numeric("price", { precision: 12, scale: 2 }), // Legacy - use askingPrice instead
	address: text("address"), // For internal use - not public
	city: text("city"),
	state: text("state"),
	zipCode: text("zip_code"),
	propertyType: text("property_type"),
	squareFootage: integer("square_footage"),
	lotSize: numeric("lot_size", { precision: 10, scale: 2 }),
	yearBuilt: integer("year_built"),
	bedrooms: integer("bedrooms"),
	bathrooms: numeric("bathrooms", { precision: 3, scale: 1 }),
	listingType: text("listing_type").default('business_sale'),
	teamVisibility: text("team_visibility").default('all'),
	internalNotes: jsonb("internal_notes").default([]),
	photos: text("photos").array(),
	documents: text("documents").array(),
	featuredPhoto: text("featured_photo"),
	virtualTourUrl: text("virtual_tour_url"),
	mlsNumber: text("mls_number"),
	listingDate: date("listing_date"), // Legacy - use dateListed instead
	expirationDate: date("expiration_date"),
	daysOnMarket: integer("days_on_market"), // Legacy - use daysListed instead
	// Draft data storage - stores incomplete listing data when status is 'draft'
	_draft: jsonb("_draft"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
},
(table) => {
	return {
		idxListingsCreatedAt: index("idx_listings_created_at").using("btree", table.createdAt.asc().nullsLast()),
		idxListingsWorkspaceId: index("idx_listings_workspace_id").using("btree", table.workspaceId.asc().nullsLast()),
		listingsWorkspaceIdFkey: foreignKey({
			columns: [table.workspaceId],
			foreignColumns: [workspaces.id],
			name: "listings_workspace_id_fkey"
		}).onDelete("cascade"),
		listingsCreatedByFkey: foreignKey({
			columns: [table.createdBy],
			foreignColumns: [users.id],
			name: "listings_created_by_fkey"
		}),
		listingsAssignedToFkey: foreignKey({
			columns: [table.assignedTo],
			foreignColumns: [users.id],
			name: "listings_assigned_to_fkey"
		}),
	}
});

export const listingDetails = pgTable("listing_details", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	listingId: uuid("listing_id").notNull(),
	// Business descriptions
	businessDescription: text("business_description"),
	briefDescription: text("brief_description"),
	// Financial details
	financialDetails: jsonb("financial_details").default({
		revenue_2023: null,
		ebitda: null,
		assets_included: [],
		inventory_value: null,
		additional_financial_info: {}
	}),
	// Operations
	operations: jsonb("operations").default({
		business_model: '',
		key_features: [],
		competitive_advantages: [],
		operational_details: {}
	}),
	// Growth and sale details
	growthOpportunities: text("growth_opportunities").array(),
	reasonForSale: text("reason_for_sale"),
	trainingPeriod: text("training_period"),
	supportType: text("support_type"),
	financingAvailable: boolean("financing_available").default(false),
	// Additional business details
	equipmentHighlights: text("equipment_highlights").array(),
	supplierRelationships: text("supplier_relationships"),
	realEstateStatus: text("real_estate_status"), // Owned, Leased, etc.
	leaseDetails: jsonb("lease_details").default({
		lease_terms: '',
		monthly_rent: null,
		lease_expiration: null,
		renewal_options: '',
		landlord_info: {}
	}),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
},
(table) => {
	return {
		idxListingDetailsListingId: index("idx_listing_details_listing_id").using("btree", table.listingId.asc().nullsLast()),
		idxListingDetailsCreatedAt: index("idx_listing_details_created_at").using("btree", table.createdAt.asc().nullsLast()),
		listingDetailsListingIdFkey: foreignKey({
			columns: [table.listingId],
			foreignColumns: [listings.id],
			name: "listing_details_listing_id_fkey"
		}).onDelete("cascade"),
		uniqueListingDetails: unique("unique_listing_details").on(table.listingId),
	}
});

export const listingStatusHistory = pgTable("listing_status_history", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	listingId: uuid("listing_id").notNull(),
	workspaceId: uuid("workspace_id").notNull(),
	changedBy: uuid("changed_by").notNull(),
	fromStatus: text("from_status"),
	toStatus: text("to_status").notNull(),
	reason: text("reason"),
	notes: text("notes"),
	metadata: jsonb("metadata").default({}),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
},
(table) => {
	return {
		idxListingStatusHistoryListingId: index("idx_listing_status_history_listing_id").using("btree", table.listingId.asc().nullsLast()),
		idxListingStatusHistoryCreatedAt: index("idx_listing_status_history_created_at").using("btree", table.createdAt.asc().nullsLast()),
		idxListingStatusHistoryChangedBy: index("idx_listing_status_history_changed_by").using("btree", table.changedBy.asc().nullsLast()),
		idxListingStatusHistoryToStatus: index("idx_listing_status_history_to_status").using("btree", table.toStatus.asc().nullsLast()),
		listingStatusHistoryListingIdFkey: foreignKey({
			columns: [table.listingId],
			foreignColumns: [listings.id],
			name: "listing_status_history_listing_id_fkey"
		}).onDelete("cascade"),
		listingStatusHistoryWorkspaceIdFkey: foreignKey({
			columns: [table.workspaceId],
			foreignColumns: [workspaces.id],
			name: "listing_status_history_workspace_id_fkey"
		}).onDelete("cascade"),
		listingStatusHistoryChangedByFkey: foreignKey({
			columns: [table.changedBy],
			foreignColumns: [users.id],
			name: "listing_status_history_changed_by_fkey"
		}),
	}
});

export const userProfiles = pgTable("user_profiles", {
	displayName: text("display_name"),
	bio: text("bio"),
	isActive: boolean("is_active").default(true).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	workspaceId: uuid("workspace_id"),
	email: text("email"),
	firstName: text("first_name"),
	lastName: text("last_name"),
	role: text("role"),
	phone: text("phone"),
	licenseNumber: text("license_number"),
	avatarUrl: text("avatar_url"),
	specialties: text("specialties").array(),
	invitedAt: timestamp("invited_at", { withTimezone: true, mode: 'string' }),
	joinedAt: timestamp("joined_at", { withTimezone: true, mode: 'string' }),
	invitedBy: uuid("invited_by"),
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	userId: uuid("user_id"),
	preferences: jsonb("preferences").default({
		notifications: {
			email_notifications: true,
			push_notifications: true,
			listing_updates: true,
			team_updates: true,
			system_updates: true
		},
		display: {
			timezone: 'America/New_York',
			date_format: 'MM/DD/YYYY',
			currency: 'USD',
			language: 'en'
		},
		privacy: {
			profile_visibility: 'team',
			contact_visibility: 'team'
		}
	}),
	lastLoginAt: timestamp("last_login_at", { withTimezone: true, mode: 'string' }),
},
(table) => {
	return {
		idxUserProfilesActive: index("idx_user_profiles_active").using("btree", table.isActive.asc().nullsLast()).where(sql`(is_active = true)`),
		idxUserProfilesEmail: index("idx_user_profiles_email").using("btree", table.email.asc().nullsLast()),
		idxUserProfilesRole: index("idx_user_profiles_role").using("btree", table.role.asc().nullsLast()),
		idxUserProfilesUserId: index("idx_user_profiles_user_id").using("btree", table.id.asc().nullsLast()),
		idxUserProfilesWorkspaceId: index("idx_user_profiles_workspace_id").using("btree", table.workspaceId.asc().nullsLast()),
		userProfilesWorkspaceIdFkey: foreignKey({
			columns: [table.workspaceId],
			foreignColumns: [workspaces.id],
			name: "user_profiles_workspace_id_fkey"
		}).onDelete("cascade"),
		userProfilesUserIdFkey: foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "user_profiles_user_id_fkey"
		}).onDelete("cascade"),
		uniqueWorkspaceEmail: unique("unique_workspace_email").on(table.workspaceId, table.email),
	}
});

export const notifications = pgTable("notifications", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	workspaceId: uuid("workspace_id").notNull(),
	userId: uuid("user_id").notNull(),
	type: text("type").notNull(),
	title: text("title").notNull(),
	message: text("message").notNull(),
	data: jsonb("data").default({}),
	entityType: text("entity_type"),
	entityId: uuid("entity_id"),
	isRead: boolean("is_read").default(false),
	readAt: timestamp("read_at", { withTimezone: true, mode: 'string' }),
	priority: text("priority").default('normal'),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
},
(table) => {
	return {
		idxNotificationsCreatedAt: index("idx_notifications_created_at").using("btree", table.createdAt.asc().nullsLast()),
		idxNotificationsEntity: index("idx_notifications_entity").using("btree", table.entityType.asc().nullsLast(), table.entityId.asc().nullsLast()),
		idxNotificationsIsRead: index("idx_notifications_is_read").using("btree", table.isRead.asc().nullsLast()),
		idxNotificationsPriority: index("idx_notifications_priority").using("btree", table.priority.asc().nullsLast()),
		idxNotificationsType: index("idx_notifications_type").using("btree", table.type.asc().nullsLast()),
		idxNotificationsUserId: index("idx_notifications_user_id").using("btree", table.userId.asc().nullsLast()),
		idxNotificationsWorkspaceId: index("idx_notifications_workspace_id").using("btree", table.workspaceId.asc().nullsLast()),
		notificationsWorkspaceIdFkey: foreignKey({
			columns: [table.workspaceId],
			foreignColumns: [workspaces.id],
			name: "notifications_workspace_id_fkey"
		}).onDelete("cascade"),
		notificationsUserIdFkey: foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "notifications_user_id_fkey"
		}).onDelete("cascade"),
	}
});

export const apiLogs = pgTable("_log", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	// Request information
	method: text("method").notNull(),
	url: text("url").notNull(),
	path: text("path").notNull(),
	userAgent: text("user_agent"),
	ipAddress: text("ip_address"),
	// User context
	userId: uuid("user_id"),
	workspaceId: uuid("workspace_id"),
	// Request data
	headers: jsonb("headers"),
	queryParams: jsonb("query_params"),
	requestBody: jsonb("request_body"),
	// Response data
	statusCode: integer("status_code"),
	responseBody: jsonb("response_body"),
	responseHeaders: jsonb("response_headers"),
	// Timing
	startTime: timestamp("start_time", { withTimezone: true, mode: 'string' }).notNull(),
	endTime: timestamp("end_time", { withTimezone: true, mode: 'string' }),
	duration: integer("duration"), // milliseconds
	// Error information
	errorMessage: text("error_message"),
	errorStack: text("error_stack"),
	// Metadata
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
},
(table) => {
	return {
		idxApiLogsCreatedAt: index("idx_api_logs_created_at").using("btree", table.createdAt.desc().nullsLast()),
		idxApiLogsUserId: index("idx_api_logs_user_id").using("btree", table.userId.asc().nullsLast()),
		idxApiLogsWorkspaceId: index("idx_api_logs_workspace_id").using("btree", table.workspaceId.asc().nullsLast()),
		idxApiLogsStatusCode: index("idx_api_logs_status_code").using("btree", table.statusCode.asc().nullsLast()),
		idxApiLogsMethod: index("idx_api_logs_method").using("btree", table.method.asc().nullsLast()),
		idxApiLogsPath: index("idx_api_logs_path").using("btree", table.path.asc().nullsLast()),
		// Composite indexes for optimized path-based queries
		idxApiLogsPathWorkspace: index("idx_api_logs_path_workspace").using("btree", table.path.asc().nullsLast(), table.workspaceId.asc().nullsLast()),
		idxApiLogsPathCreatedAt: index("idx_api_logs_path_created_at").using("btree", table.path.asc().nullsLast(), table.createdAt.desc().nullsLast()),
		idxApiLogsPathMethod: index("idx_api_logs_path_method").using("btree", table.path.asc().nullsLast(), table.method.asc().nullsLast()),
		idxApiLogsPathStatus: index("idx_api_logs_path_status").using("btree", table.path.asc().nullsLast(), table.statusCode.asc().nullsLast()),
		// Foreign keys for user and workspace if they exist
		apiLogsUserIdFkey: foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "api_logs_user_id_fkey"
		}),
		apiLogsWorkspaceIdFkey: foreignKey({
			columns: [table.workspaceId],
			foreignColumns: [workspaces.id],
			name: "api_logs_workspace_id_fkey"
		}).onDelete("cascade"),
	}
});