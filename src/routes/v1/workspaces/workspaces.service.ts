import { eq, and, desc, sql } from "drizzle-orm";
import db from "@/db";
import { workspaces, workspaceInvitations } from "@/db/schema";
import env from "@/env";
import crypto from "node:crypto";

export class WorkspacesService {
  // Workspace operations
  static async getWorkspace(workspaceId: string) {
    const workspace = await db
      .select()
      .from(workspaces)
      .where(eq(workspaces.id, workspaceId))
      .limit(1);

    if (!workspace[0]) {
      throw new Error("Workspace not found");
    }

    return workspace[0];
  }

  static async updateWorkspace(workspaceId: string, data: any) {
    const updatedWorkspace = await db
      .update(workspaces)
      .set({
        ...data,
        updatedAt: new Date().toISOString(),
      })
      .where(eq(workspaces.id, workspaceId))
      .returning();

    if (!updatedWorkspace[0]) {
      throw new Error("Failed to update workspace");
    }

    return updatedWorkspace[0];
  }

  // Invitation operations
  static async listInvitations(
    workspaceId: string,
    options: {
      page: number;
      limit: number;
      status?: string;
      role?: string;
    }
  ) {
    const { page, limit, status, role } = options;
    const offset = (page - 1) * limit;

    let query = db
      .select()
      .from(workspaceInvitations)
      .where(eq(workspaceInvitations.workspaceId, workspaceId))
      .orderBy(desc(workspaceInvitations.createdAt))
      .limit(limit)
      .offset(offset);

    // Apply filters if provided
    // Note: This is a simplified example - you would add proper filtering logic

    const invitations = await query;

    // Get total count for pagination
    const totalResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(workspaceInvitations)
      .where(eq(workspaceInvitations.workspaceId, workspaceId));

    const total = totalResult[0]?.count || 0;
    const pages = Math.ceil(total / limit);

    return {
      data: invitations.map(invitation => ({
        ...invitation,
        canResend: this.canResendInvitation(invitation),
        isExpired: this.isInvitationExpired(invitation),
      })),
      pagination: {
        page,
        limit,
        total,
        pages,
      },
    };
  }

  static async createInvitation(
    workspaceId: string,
    invitedBy: string,
    data: { email: string; role: string },
    redirectTo: string
  ) {
    return await db.transaction(async (tx) => {
      // console.log(`[createInvitation] Starting invitation creation process`, {
      //   workspaceId,
      //   invitedBy,
      //   email: data.email,
      //   role: data.role,
      //   timestamp: new Date().toISOString()
      // });

      // // Check if user already exists or has pending invitation
      // console.log(`[createInvitation] Checking for existing invitation`, {
      //   workspaceId,
      //   email: data.email
      // });

      const existingInvitation = await tx
        .select()
        .from(workspaceInvitations)
        .where(
          and(
            eq(workspaceInvitations.workspaceId, workspaceId),
            eq(workspaceInvitations.email, data.email)
          )
        )
        .limit(1);

      // console.log(`[createInvitation] Existing invitation check result`, {
      //   found: !!existingInvitation[0],
      //   invitationId: existingInvitation[0]?.id || null,
      //   acceptedAt: existingInvitation[0]?.acceptedAt || null
      // });

      if (existingInvitation[0]) {
        // console.log(`[createInvitation] Duplicate invitation detected`, {
        //   existingInvitationId: existingInvitation[0].id,
        //   acceptedAt: existingInvitation[0].acceptedAt,
        //   email: data.email,
        //   workspaceId
        // });
        throw new Error("User already invited or exists in workspace");
      }

      // console.log(`[createInvitation] No existing invitation found, proceeding with creation`);

      // Generate invitation token
      const invitationToken = this.generateInvitationToken();
      // console.log(`[createInvitation] Generated invitation token`, {
      //   tokenLength: invitationToken.length,
      //   tokenPrefix: invitationToken.substring(0, 8) + '...'
      // });

      // Calculate expiration date
      const expirationDate = this.getExpirationDate();
      // console.log(`[createInvitation] Calculated expiration date`, {
      //   expiresAt: expirationDate,
      //   daysFromNow: 7
      // });

      // Create new invitation
      // console.log(`[createInvitation] Inserting new invitation into database`, {
      //   workspaceId,
      //   email: data.email,
      //   role: data.role,
      //   invitedBy,
      //   expiresAt: expirationDate
      // });

      const invitation = await tx
        .insert(workspaceInvitations)
        .values({
          workspaceId,
          email: data.email,
          role: data.role,
          invitedBy,
          token: invitationToken,
          expiresAt: expirationDate,
        })
        .returning();

      // console.log(`[createInvitation] Database insertion completed`, {
      //   success: !!invitation[0],
      //   invitationId: invitation[0]?.id || null,
      //   rowsAffected: invitation.length
      // });

      if (!invitation[0]) {
        // console.error(`[createInvitation] Database insertion failed`, {
        //   workspaceId,
        //   email: data.email,
        //   role: data.role
        // });
        throw new Error("Failed to create invitation");
      }

      // console.log(`[createInvitation] Invitation created successfully`, {
      //   invitationId: invitation[0].id,
      //   email: invitation[0].email,
      //   role: invitation[0].role,
      //   acceptedAt: invitation[0].acceptedAt,
      //   expiresAt: invitation[0].expiresAt
      // });

      // Send invitation email
      await this.sendInvitationEmail(redirectTo, invitation[0]);

      const result = {
        ...invitation[0],
        canResend: true,
        isExpired: false,
      };

      // console.log(`[createInvitation] Returning final invitation result`, {
      //   invitationId: result.id,
      //   canResend: result.canResend,
      //   isExpired: result.isExpired,
      //   totalSteps: 6
      // });

      return result;
    }
  );
  }

  static async updateInvitation(
    invitationId: string,
    workspaceId: string,
    data: { role?: string }
  ) {
    const updatedInvitation = await db
      .update(workspaceInvitations)
      .set(data)
      .where(
        and(
          eq(workspaceInvitations.id, invitationId),
          eq(workspaceInvitations.workspaceId, workspaceId)
        )
      )
      .returning();

    if (!updatedInvitation[0]) {
      throw new Error("Invitation not found or failed to update");
    }

    return {
      ...updatedInvitation[0],
      canResend: this.canResendInvitation(updatedInvitation[0]),
      isExpired: this.isInvitationExpired(updatedInvitation[0]),
    };
  }

  static async deleteInvitation(invitationId: string, workspaceId: string) {
    const deletedInvitation = await db
      .delete(workspaceInvitations)
      .where(
        and(
          eq(workspaceInvitations.id, invitationId),
          eq(workspaceInvitations.workspaceId, workspaceId)
        )
      )
      .returning();

    if (!deletedInvitation[0]) {
      throw new Error("Invitation not found");
    }

    return deletedInvitation[0];
  }

  static async resendInvitation(invitationId: string, workspaceId: string, redirectTo: string) {
    // Check rate limiting
    const invitation = await db
      .select()
      .from(workspaceInvitations)
      .where(
        and(
          eq(workspaceInvitations.id, invitationId),
          eq(workspaceInvitations.workspaceId, workspaceId)
        )
      )
      .limit(1);

    if (!invitation[0]) {
      throw new Error("Invitation not found");
    }

    // Simple rate limiting check (can be enhanced)
    const lastUpdated = new Date(invitation[0].createdAt || new Date());
    const now = new Date();
    const timeDiff = now.getTime() - lastUpdated.getTime();
    const hoursDiff = timeDiff / (1000 * 60 * 60);

    if (hoursDiff < 1) {
      throw new Error("Rate limit exceeded. Wait 1 hour between resends.");
    }

    // Update invitation with new token and expiration
    const updatedInvitation = await db
      .update(workspaceInvitations)
      .set({
        token: this.generateInvitationToken(),
        expiresAt: this.getExpirationDate(),
      })
      .where(eq(workspaceInvitations.id, invitationId))
      .returning();

    // Send invitation email
    await this.sendInvitationEmail(redirectTo, updatedInvitation[0]);

    return {
      ...updatedInvitation[0],
      canResend: true,
      isExpired: false,
    };
  }

  // Helper methods
  private static generateInvitationToken(): string {
    return crypto.randomBytes(24).toString('hex');
  }

  private static getExpirationDate(): string {
    const date = new Date();
    date.setDate(date.getDate() + 7); // 7 days from now
    return date.toISOString();
  }

  private static canResendInvitation(invitation: any): boolean {
    // Can resend if not expired and not accepted
    return !this.isInvitationExpired(invitation) && 
           !invitation.acceptedAt;
  }

  private static isInvitationExpired(invitation: any): boolean {
    if (!invitation.expiresAt) return false;
    return new Date(invitation.expiresAt) < new Date();
  }

  private static async sendInvitationEmail(redirectTo: string, invitation: any) {
    try {
      // Get workspace details for the invitation email
      const workspace = await db
        .select()
        .from(workspaces)
        .where(eq(workspaces.id, invitation.workspaceId))
        .limit(1);

      if (!workspace[0]) {
        throw new Error("Workspace not found");
      }

      const inviteLink = `${redirectTo}/invite/accept?token=${invitation.token}&workspace=${workspace[0].id}`;
      console.log('[sendInvitationEmail] Generated invite link', {
        email: invitation.email,
        workspaceId: invitation.workspaceId,
        inviteLink,
      });
      return { inviteLink } as any;
    } catch (error) {
      console.error('[sendInvitationEmail] Error sending invitation email:', error);
      throw error;
    }
  }


} 